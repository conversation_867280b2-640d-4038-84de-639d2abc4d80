# <PERSON>ro AI Assistant System Documentation

This folder contains documentation about <PERSON><PERSON>'s capabilities, guidelines, and operational standards.

## Contents

- **[capabilities.md](capabilities.md)** - Core capabilities and features
- **[guidelines.md](guidelines.md)** - Operational guidelines and standards
- **[response-style.md](response-style.md)** - Communication style and tone
- **[quality-standards.md](quality-standards.md)** - Code and output quality requirements
- **[workflow-patterns.md](workflow-patterns.md)** - Common workflow patterns and approaches

## Purpose

These documents serve as reference material for understanding how Kiro operates and what quality standards are maintained when assisting with development tasks.

## Integration with Spec Process

Kiro's operational standards complement the spec-driven development process by ensuring that all assistance provided meets high quality standards and follows consistent patterns.