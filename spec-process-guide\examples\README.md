# Examples

<!-- Navigation Metadata -->
<!-- Section: Examples | Level: Reference | Prerequisites: methodology/README.md -->
<!-- Related: templates/README.md, process/README.md, ai-reasoning/examples.md -->

**📍 You are here:** [Main Guide](../../README.md) → **Examples**

## Quick Navigation
- **Learn First:** [Methodology Overview](../methodology/README.md) - Understand the foundation
- **Get Templates:** [Ready-to-Use Templates](../templates/README.md) - Start your own specs
- **Follow Process:** [Process Guide](../process/README.md) - Step-by-step instructions
- **AI Insights:** [AI Reasoning Examples](../ai-reasoning/examples.md) - See decision-making in action

---

Real-world case studies and complete spec examples showing the methodology in action.

## In This Section

- **[Simple Feature Specs](simple-feature-spec.md)** - Complete examples for basic features
- **[Complex System Specs](complex-system-spec.md)** - Large-scale system development examples  
- **[Case Studies](case-studies.md)** - Success stories and lessons learned
- **[Troubleshooting & Pitfalls](troubleshooting-pitfalls.md)** - Common mistakes and recovery strategies

## Learning from Examples

Each example includes:
- **Complete Spec Trilogy** - Requirements, Design, and Tasks documents
- **Decision Commentary** - Explanation of key choices and trade-offs
- **Implementation Notes** - How the spec translated to actual code
- **Lessons Learned** - What worked well and what could be improved

## Example Categories

### Simple Features
- User authentication system
- Data validation component
- API endpoint creation
- Form handling logic

### Complex Systems
- Multi-service API architecture
- Data processing pipeline
- Real-time notification system
- Content management platform

### Domain-Specific Examples
- E-commerce checkout flow
- Financial transaction processing
- Healthcare data management
- Educational content delivery

---

[← Back to Main Guide](../../README.md) | [Start with Simple Examples →](simple-feature-spec.md)