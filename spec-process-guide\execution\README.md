# Execution Guide by kiro

<!-- Navigation Metadata -->
<!-- Section: Execution | Level: Overview | Prerequisites: process/tasks-phase.md -->
<!-- Related: examples/simple-feature-spec.md, resources/tools.md, process/README.md -->

**📍 You are here:** [Main Guide](../../README.md) → **Execution Guide**

## Quick Navigation
- **Prerequisites:** [Tasks Phase](../process/tasks-phase.md) - Learn how to create implementation plans
- **Complete Example:** [Simple Feature Spec](../examples/simple-feature-spec.md) - See full spec-to-code workflow
- **Helpful Tools:** [Tools & Resources](../resources/tools.md) - Recommended execution tools
- **Process Overview:** [Three-Phase Workflow](../process/README.md) - Understand the full context

---

Practical guidance for implementing features from completed specs.

## In This Section

- **[Implementation Guide](implementation-guide.md)** - Step-by-step execution strategies
- **[Quality Assurance](quality-assurance.md)** - Testing and validation techniques
- **[Troubleshooting](troubleshooting.md)** - Common issues and solutions

## From Spec to Code

Once you have a completed spec with requirements, design, and tasks, this section guides you through:

- **Task Execution** - How to work through implementation tasks systematically
- **Quality Gates** - Validation checkpoints to maintain code quality
- **Progress Tracking** - Managing task completion and dependencies
- **Adaptation Strategies** - Handling unexpected challenges during implementation

## Execution Principles

1. **One Task at a Time** - Focus on individual tasks to maintain quality
2. **Validate Early** - Test components as you build them
3. **Document Changes** - Track deviations from the original plan
4. **Maintain Momentum** - Keep implementation moving while ensuring quality

---

[← Back to Main Guide](../../README.md) | [Start Implementation →](implementation-guide.md)