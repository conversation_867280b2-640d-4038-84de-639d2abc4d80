# Prompting Strategies

<!-- Navigation Metadata -->
<!-- Section: Prompting | Level: Overview | Prerequisites: methodology/README.md -->
<!-- Related: process/README.md, ai-reasoning/decision-frameworks.md, templates/README.md -->

**📍 You are here:** [Main Guide](../../README.md) → **Prompting Strategies**

## Quick Navigation
- **Foundation:** [Methodology Overview](../methodology/README.md) - Understand spec-driven development first
- **Process Steps:** [Process Guide](../process/README.md) - Learn the three-phase workflow
- **AI Reasoning:** [Decision Frameworks](../ai-reasoning/decision-frameworks.md) - Understand how AI makes choices
- **Practice:** [Templates](../templates/README.md) - Try prompting with structured templates

---

Effective communication techniques for successful AI collaboration during spec development.

## In This Section

- **[Strategies](strategies.md)** - Core approaches for clear, effective prompting
- **[Templates](templates.md)** - Ready-to-use prompt patterns for each phase
- **[Best Practices](best-practices.md)** - Tips for getting better results

## Key Principles

Effective prompting for spec development follows these principles:

1. **Be Specific** - Provide clear context and concrete examples
2. **Structure Requests** - Break complex asks into manageable parts
3. **Iterate Thoughtfully** - Build on previous responses rather than starting over
4. **Validate Understanding** - Confirm alignment before proceeding to next phases

## Common Patterns

- **Context Setting** - Establishing project background and constraints
- **Phase Transitions** - Moving smoothly between requirements, design, and tasks
- **Feedback Integration** - Incorporating changes and refinements effectively
- **Quality Validation** - Ensuring outputs meet your standards

---

[← Back to Main Guide](../../README.md) | [Learn Core Strategies →](strategies.md)