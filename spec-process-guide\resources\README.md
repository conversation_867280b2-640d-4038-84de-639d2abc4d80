# Resources

<!-- Navigation Metadata -->
<!-- Section: Resources | Level: Reference | Prerequisites: None -->
<!-- Related: process/requirements-phase.md, templates/README.md, methodology/README.md -->

**📍 You are here:** [Main Guide](../../README.md) → **Resources**

## Quick Navigation
- **Apply Standards:** [Requirements Phase](../process/requirements-phase.md) - Use EARS format in practice
- **Get Templates:** [Templates & Checklists](../templates/README.md) - Ready-to-use starting points
- **Understand Context:** [Methodology](../methodology/README.md) - See how resources fit the bigger picture
- **Find Tools:** [Tool Integration Guide](tool-integration-guide.md) - Specific tool recommendations

---

Curated references and learning materials to deepen your understanding of spec-driven development.

## In This Section

- **[Standards](standards.md)** - EARS format and industry requirements engineering standards
- **[Tools](tools.md)** - Recommended tools and integrations for spec development
- **[Further Reading](further-reading.md)** - Books, articles, and additional learning resources

## Quick Reference

### EARS Format
**E**asy **A**pproach to **R**equirements **S**yntax - A structured way to write clear, testable requirements using keywords like WHEN, IF, WHILE, WHERE, and SHALL.

### Key Standards
- IEEE 830 - Software Requirements Specifications
- ISO/IEC 25010 - Systems and software Quality Requirements and Evaluation
- Agile Requirements Engineering practices

### Essential Tools
- Documentation platforms (Markdown, Notion, Confluence)
- Diagramming tools (Mermaid, Lucidchart, Draw.io)
- Project management (Linear, Jira, GitHub Issues)

---

[← Back to Main Guide](../../README.md) | [Explore Standards →](standards.md)