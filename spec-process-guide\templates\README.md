# Templates

<!-- Navigation Metadata -->
<!-- Section: Templates | Level: Reference | Prerequisites: None -->
<!-- Related: process/README.md, examples/README.md, resources/standards.md -->

**📍 You are here:** [Main Guide](../../README.md) → **Templates**

## Quick Navigation
- **Learn Process:** [Process Guide](../process/README.md) - Understand how to use these templates
- **See Examples:** [Complete Examples](../examples/README.md) - Templates filled out in practice
- **Standards Reference:** [EARS & Standards](../resources/standards.md) - Format guidelines
- **Start Here:** [Requirements Template](requirements-template.md) - Begin your first spec

---

Ready-to-use templates and checklists to accelerate your spec development process.

## In This Section

### Full Spec Templates
- **[Requirements Template](requirements-template.md)** - EARS-formatted requirements structure
- **[Design Template](design-template.md)** - Comprehensive design document framework
- **[Tasks Template](tasks-template.md)** - Implementation planning format

### Lightweight Templates
- **[Quick Spec Template](quick-spec-template.md)** - For 1-3 day features (requirements + tasks only)
- **[Micro Spec Template](micro-spec-template.md)** - For sub-1 day changes (minimal documentation)

### Supporting Templates
- **[Checklists](checklists.md)** - Quality gates and validation checklists

## How to Use Templates

1. **Copy the Template** - Start with the appropriate template for your phase
2. **Customize Sections** - Adapt the structure to your specific feature needs
3. **Fill in Content** - Replace placeholder text with your actual requirements/design/tasks
4. **Validate Completeness** - Use the included checklists to ensure nothing is missed

## Template Features

Each template includes:
- **Structured Format** - Consistent organization and formatting
- **Placeholder Content** - Examples to guide your writing
- **Validation Checklists** - Quality gates for each section
- **Cross-References** - Links between related sections

## Quick Start Guide

1. **New Feature?** Start with [Requirements Template](requirements-template.md)
2. **Requirements Done?** Move to [Design Template](design-template.md)  
3. **Design Complete?** Use [Tasks Template](tasks-template.md)
4. **Need Examples?** Check the [Examples](../examples/README.md) section

---

[← Back to Main Guide](../../README.md) | [Get Requirements Template →](requirements-template.md)